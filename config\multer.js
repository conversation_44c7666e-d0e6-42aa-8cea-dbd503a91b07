const multer = require("multer");
const path = require("path");
const fs = require("fs");

// تكوين multer لرفع صور الموظفين
const employeePhotoStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    const employeeCode = req.params.employeeCode || req.body.employee_code;
    const uploadPath = path.join(__dirname, '..', 'uploads', 'employees', employeeCode.toString());

    // إنشاء المجلد إذا لم يكن موجوداً
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }

    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    // استخدام اسم ثابت للصورة
    const ext = path.extname(file.originalname);
    cb(null, 'photo' + ext);
  }
});

// تكوين multer لرفع مستندات الموظفين
const employeeDocumentStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    const employeeCode = req.params.employeeCode || req.body.employee_code;
    const uploadPath = path.join(__dirname, '..', 'uploads', 'employees', employeeCode.toString());

    // إنشاء المجلد إذا لم يكن موجوداً
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }

    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    // إنشاء اسم فريد للمستند
    const timestamp = Date.now();
    const ext = path.extname(file.originalname);
    const name = path.basename(file.originalname, ext);
    cb(null, `${name}_${timestamp}${ext}`);
  }
});

// فلاتر للتحقق من نوع الملفات
const photoFileFilter = (req, file, cb) => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('نوع الملف غير مدعوم. يُسمح فقط بملفات JPG و PNG'), false);
  }
};

const documentFileFilter = (req, file, cb) => {
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'image/jpeg',
    'image/jpg',
    'image/png',
    'text/plain'
  ];

  // التحقق من الامتداد أيضاً للتأكد
  const allowedExtensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.jpg', '.jpeg', '.png', '.txt'];
  const fileExtension = path.extname(file.originalname).toLowerCase();

  if (allowedTypes.includes(file.mimetype) || allowedExtensions.includes(fileExtension)) {
    cb(null, true);
  } else {
    cb(new Error('نوع الملف غير مدعوم. الأنواع المدعومة: PDF, DOC, DOCX, XLS, XLSX, JPG, PNG, TXT'), false);
  }
};

// إعداد multer للصور والمستندات
const uploadEmployeePhoto = multer({
  storage: employeePhotoStorage,
  fileFilter: photoFileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB
  }
});

const uploadEmployeeDocument = multer({
  storage: employeeDocumentStorage,
  fileFilter: documentFileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB
  }
});

// تكوين multer عام لمعالجة الملفات
const upload = multer({ dest: 'uploads/' });

module.exports = {
  upload,
  uploadEmployeePhoto,
  uploadEmployeeDocument,
  employeePhotoStorage,
  employeeDocumentStorage,
  photoFileFilter,
  documentFileFilter
};
