/**
 * Middleware لفلترة البيانات حسب صلاحيات الإدارات
 */

const { pool } = require('../config/database');

/**
 * دالة للحصول على الإدارات المسموح للمستخدم برؤيتها
 * @param {number} userId - معرف المستخدم
 * @returns {Promise<Array>} - قائمة الإدارات المسموحة
 */
async function getUserAllowedDepartments(userId) {
  try {
    console.log(`🔍 Getting allowed departments for user ID: ${userId}`);

    // التحقق من إعداد رؤية جميع الإدارات
    const [userInfo] = await pool.promise().query(`
      SELECT username, view_all_departments
      FROM users
      WHERE id = ?
    `, [userId]);

    console.log(`👤 User info from database:`, userInfo[0]);

    if (userInfo.length === 0) {
      console.log(`❌ User not found in database`);
      return [];
    }

    const user = userInfo[0];

    // السماح للمستخدم admin برؤية جميع الإدارات دائماً
    if (user.username === 'admin' || user.view_all_departments) {
      console.log(`👑 User has admin privileges or view_all_departments = 1`);
      // للمستخدم admin أو من لديه view_all_departments، نرجع null لعدم تطبيق فلترة
      return null;
    } else {
      // الحصول على الإدارات المحددة
      console.log(`🔒 User has limited department access, checking user_department_permissions`);
      const [userDepartments] = await pool.promise().query(`
        SELECT DISTINCT department as name
        FROM user_department_permissions
        WHERE user_id = ? AND can_view = 1
      `, [userId]);
      console.log(`📋 Found ${userDepartments.length} specific departments for user`);
      const userDepartmentNames = userDepartments.map(dept => dept.name);
      console.log(`📋 User department names:`, userDepartmentNames);
      return userDepartmentNames;
    }
  } catch (error) {
    console.error('خطأ في جلب الإدارات المسموحة:', error);
    return [];
  }
}

/**
 * دالة لإنشاء شرط WHERE لفلترة الإدارات
 * @param {Array} allowedDepartments - قائمة الإدارات المسموحة
 * @param {string} departmentColumn - اسم عمود الإدارة في الجدول (افتراضي: 'department')
 * @returns {Object} - كائن يحتوي على شرط WHERE والمعاملات
 */
function createDepartmentFilter(allowedDepartments, departmentColumn = 'department') {
  if (!allowedDepartments || allowedDepartments.length === 0) {
    return {
      whereClause: `1 = 0`, // لن يعرض أي نتائج
      params: []
    };
  }

  const placeholders = allowedDepartments.map(() => '?').join(',');
  return {
    whereClause: `${departmentColumn} IN (${placeholders})`,
    params: allowedDepartments
  };
}

/**
 * Middleware للتحقق من صلاحيات الإدارات وإضافة قائمة الإدارات المسموحة للطلب
 */
const addDepartmentFilter = async (req, res, next) => {
  try {
    if (!req.user || !req.user.id) {
      return res.status(401).json({
        success: false,
        message: 'غير مصرح بالوصول'
      });
    }

    // الحصول على الإدارات المسموحة للمستخدم
    const allowedDepartments = await getUserAllowedDepartments(req.user.id);

    // إضافة logging مفصل للتشخيص
    console.log(`🔍 Department Filter Debug - User ID: ${req.user.id}, Username: ${req.user.username}`);
    console.log(`📋 Allowed Departments:`, allowedDepartments);
    console.log(`📊 Departments Count:`, allowedDepartments ? allowedDepartments.length : 'null');

    // إضافة الإدارات المسموحة للطلب
    req.allowedDepartments = allowedDepartments;

    // التعامل مع النتائج
    if (allowedDepartments === null) {
      // المستخدم admin أو لديه view_all_departments - لا نطبق فلترة
      req.departmentFilter = null;
      console.log(`👑 Admin user or view_all_departments - no filtering applied`);
    } else if (!allowedDepartments || allowedDepartments.length === 0) {
      // لا توجد إدارات مسموحة - منع عرض أي بيانات
      req.departmentFilter = createDepartmentFilter([]);
      console.log(`🚫 No departments allowed - blocking all data`);
    } else {
      // تطبيق فلترة الإدارات المحددة
      req.departmentFilter = createDepartmentFilter(allowedDepartments);
      console.log(`✅ Department filter applied for departments:`, allowedDepartments);
    }

    next();
  } catch (error) {
    console.error('خطأ في middleware فلترة الإدارات:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في التحقق من صلاحيات الإدارات'
    });
  }
};

/**
 * دالة للتحقق من صلاحية المستخدم لرؤية إدارة معينة
 * @param {number} userId - معرف المستخدم
 * @param {string} department - اسم الإدارة
 * @returns {Promise<boolean>} - true إذا كان مسموح، false إذا لم يكن مسموح
 */
async function canUserViewDepartment(userId, department) {
  try {
    const allowedDepartments = await getUserAllowedDepartments(userId);
    return allowedDepartments.includes(department);
  } catch (error) {
    console.error('خطأ في التحقق من صلاحية الإدارة:', error);
    return false;
  }
}

/**
 * دالة لفلترة قائمة البيانات حسب الإدارات المسموحة
 * @param {Array} data - البيانات المراد فلترتها
 * @param {Array} allowedDepartments - الإدارات المسموحة
 * @param {string} departmentField - اسم حقل الإدارة في البيانات (افتراضي: 'department')
 * @returns {Array} - البيانات المفلترة
 */
function filterDataByDepartments(data, allowedDepartments, departmentField = 'department') {
  if (!allowedDepartments || allowedDepartments.length === 0) {
    return [];
  }

  return data.filter(item => {
    const itemDepartment = item[departmentField];
    return itemDepartment && allowedDepartments.includes(itemDepartment);
  });
}

/**
 * دالة لإنشاء استعلام SQL مع فلترة الإدارات
 * @param {string} baseQuery - الاستعلام الأساسي
 * @param {Array} allowedDepartments - الإدارات المسموحة
 * @param {string} departmentColumn - اسم عمود الإدارة
 * @param {Array} baseParams - معاملات الاستعلام الأساسي
 * @returns {Object} - كائن يحتوي على الاستعلام المحدث والمعاملات
 */
function addDepartmentFilterToQuery(baseQuery, allowedDepartments, departmentColumn = 'department', baseParams = []) {
  if (!allowedDepartments || allowedDepartments.length === 0) {
    return {
      query: baseQuery + ` AND ${departmentColumn} IS NULL`, // لن يعرض أي نتائج
      params: baseParams
    };
  }

  const placeholders = allowedDepartments.map(() => '?').join(',');
  const departmentFilter = ` AND ${departmentColumn} IN (${placeholders})`;
  
  return {
    query: baseQuery + departmentFilter,
    params: [...baseParams, ...allowedDepartments]
  };
}

/**
 * دالة للتحقق من صلاحية المستخدم لإدارة إدارة معينة (إضافة/تعديل/حذف)
 * @param {number} userId - معرف المستخدم
 * @param {string} department - اسم الإدارة
 * @returns {Promise<boolean>} - true إذا كان مسموح، false إذا لم يكن مسموح
 */
async function canUserManageDepartment(userId, department) {
  try {
    // التحقق من إعداد رؤية جميع الإدارات
    const [userInfo] = await pool.promise().query(`
      SELECT view_all_departments 
      FROM users 
      WHERE id = ?
    `, [userId]);

    if (userInfo.length === 0) {
      return false;
    }

    if (userInfo[0].view_all_departments) {
      return true; // يمكنه إدارة جميع الإدارات
    }

    // التحقق من صلاحية الإدارة المحددة
    const [permission] = await pool.promise().query(`
      SELECT can_manage
      FROM user_department_permissions 
      WHERE user_id = ? AND department = ? AND can_manage = 1
    `, [userId, department]);

    return permission.length > 0;
  } catch (error) {
    console.error('خطأ في التحقق من صلاحية إدارة الإدارة:', error);
    return false;
  }
}

module.exports = {
  getUserAllowedDepartments,
  createDepartmentFilter,
  addDepartmentFilter,
  canUserViewDepartment,
  canUserManageDepartment,
  filterDataByDepartments,
  addDepartmentFilterToQuery
};
