const jwt = require("jsonwebtoken");
const { pool } = require("../config/database");

// دالة للتحقق من صحة التوكن
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'غير مصرح' });
  }

  jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key', (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'توكن غير صالح' });
    }
    req.user = user;
    next();
  });
};

// دالة للتحقق من الصلاحيات
const checkPermission = (permission) => {
  return async (req, res, next) => {
    try {
      // التحقق من وجود المستخدم في الطلب (من authenticateToken)
      if (!req.user || !req.user.id) {
        return res.status(401).json({ error: 'غير مصرح' });
      }

      // الحصول على بيانات المستخدم من قاعدة البيانات
      const [rows] = await pool.promise().query(
        "SELECT * FROM users WHERE id = ?",
        [req.user.id]
      );

      if (rows.length === 0) {
        return res.status(401).json({ error: 'المستخدم غير موجود' });
      }

      const user = rows[0];
      let permissions = {};

      // التحقق من وجود الصلاحيات وتحويلها إلى كائن JavaScript
      if (user.permissions) {
        permissions = typeof user.permissions === 'string' 
          ? JSON.parse(user.permissions) 
          : user.permissions;
      }

      // إذا كان المستخدم هو admin، نسمح له بالوصول دائمًا
      if (user.username === 'admin') {
        return next();
      }

      // التحقق من وجود الصلاحية المطلوبة
      if (!permissions[permission]) {
        return res.status(403).json({
          error: 'ليس لديك الصلاحية الكافية للقيام بهذه العملية',
          requiredPermission: permission
        });
      }

      // إذا كان لديه الصلاحية، نسمح له بالمتابعة
      next();
    } catch (error) {
      console.error('خطأ في التحقق من الصلاحيات:', error);
      res.status(500).json({ error: 'حدث خطأ أثناء التحقق من الصلاحيات' });
    }
  };
};

module.exports = {
  authenticateToken,
  checkPermission
};